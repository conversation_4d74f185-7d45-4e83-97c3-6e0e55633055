resource "aws_ecs_task_definition" "default" {
  family                   = "${var.task_friendly_name}_${var.environment}"
  container_definitions    = data.template_file.container_definition.rendered
  task_role_arn            = var.task_role_arn
  execution_role_arn       = var.execution_role_arn
  requires_compatibilities = [var.requires_compatibilites]

  volume {
    name      = "aisdata"
    host_path = "/aisdata"
  }
}

data "template_file" "container_definition" {
  template = file(var.container_definition_path)
  vars = {
    environment           = var.environment
    image_url_name_tag    = var.image_url_name_tag
    country_iso_code      = var.country_iso_code
    task_friendly_name    = var.task_friendly_name
    region                = var.region
    rrri_topic_arn        = var.rrri_topic_arn
    db_password_arn            = data.aws_ssm_parameter.db_password.arn
    email_smtp_password_arn    = data.aws_ssm_parameter.email_smtp_password.arn
  }
}

data "aws_ssm_parameter" "db_password" {
  name = "/ais/${var.environment}/us/describe-vehicle-export/db/password"
  with_decryption = true
}

data "aws_ssm_parameter" "email_smtp_password" {
  name = "/ais/${var.environment}/us/describe-vehicle-export/email/smtp-password"
  with_decryption = true
}