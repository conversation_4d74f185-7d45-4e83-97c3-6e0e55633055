module "task" {
  source = "./task"

  environment               = var.environment
  region                    = var.region
  country_iso_code          = var.country_iso_code
  task_friendly_name        = var.task_friendly_name
  container_definition_path = var.container_definition_path
  task_role_arn             = var.task_role_arn
  execution_role_arn        = var.execution_role_arn
  requires_compatibilites   = var.requires_compatibilites
  image_url_name_tag        = var.image_url_name_tag
  rrri_topic_arn            = var.rrri_topic_arn
}

module "log-group" {
  source = "./log-group"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  name              = var.task_friendly_name
  log_group_name    = "${var.task_friendly_name}_${var.environment}"
  retention_in_days = var.retention_in_days
}

module "scheduled-event" {
  source = "./scheduled-event"

  environment         = var.environment
  task_friendly_name  = var.task_friendly_name
  schedule_expression = var.schedule_expression
  role_arn            = var.event_rule_arn
  task_arn            = module.task.task_arn
  cluster_arn         = var.cluster_arn
  task_count          = var.task_count
  enabled             = var.enabled
}

module "alarm" {
  source = "./alarm"

  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  component     = var.component

  name                  = var.task_friendly_name
  log_group_name        = module.log-group.log_group_name
  alarm_action_arn      = var.alarm_action_arn
  alarm_description     = var.alarm_description
  metric_filter_pattern = var.alarm_metric_filter_pattern
}

