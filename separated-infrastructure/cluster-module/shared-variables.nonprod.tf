#==================================================================
# Shared Variables for Cross Regions in Non-Production Account - Cluster Module
#==================================================================

variable "account_type" {
  description = "Type of AWS account"
  default     = "nonprod"
}

variable "account_id" {
  description = "ID of AWS account"
  default     = "************"
}

variable "account_name" {
  description = "Name of AWS account"
  default     = "awsaaianp"
}

variable "efs_shares" {
  # WARNING: When you change EFS IDs, you must update the Consumer/Internal Webstack AMI which premounts it
  description = "EFS shares used as general storage for the entire application"
  type        = map(string)
  default = {
    "us-east-1" = "fs-5fcfafaa"
  }
}

variable "package_bucket_names" {
  description = "The S3 bucket that code and INI should be pulled from"
  type        = map(string)
  default = {
    "us-east-1" = "ais.1-0.application.packages.np.ue1"
  }
}

variable "efs_security_group" {
  description = "The security group that has access to the efs"
  type        = map(string)
  default = {
    "us-east-1" = "sg-0358b7ef2c51fcbd1"
  }
}

####https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-optimized_AMI.html
variable "ecs_ami_ids" {
  description = "Region specific AMI IDs for ECS (amzn2-ami-ecs-hvm-2.0.********-x86_64-ebs)"
  type        = map(string)
  default = {
    "us-east-1" = "ami-0fe5f366c083f59ca"
  }
}

###############################################################
# Autoscaling Group Schedule Variables
###############################################################
# Active Periods (plus extra hour on each for DST): 
# UAT (Beta/Ext01): M-F or 1st/2nd of month @ 5am-10pm EST
# Scratch: M-F @ 10am-9pm UTC+5:30
# Non-Oxlo/India (default): M-F @ 7am-7pm EST
# UAT (Alpha): M-F or 1st/2nd of month @ 08AM - 01AM(next day) (EST)
# NOTE: In rds_scheduling_map to handle the next day (EST) scenario for UAT-Alpha, we used the UTC-06:00 timezone instead so that the range can lie into single day
###############################################################

variable "autoscale_scheduling_normal_map" {
  description = "Cron map representation of autoscale scheduling for normal business hour scale-up/down"
  type        = map(string)
  default = {
    "default.morning"   = "30 4 * * 1-5"
    "default.night"     = "30 15 * * *"
    "uat-alpha.morning" = "0 12 * * 1-5"
    "uat-alpha.night"   = "0 6 * * *"
    "uat-beta.morning"  = "0 4 * * 1-5"
    "uat-beta.night"    = "0 3 * * *"
    "uat-ext01.morning" = "0 9 * * 1-5"
    "uat-ext01.night"   = "0 3 * * *"
    "uat-ext02.morning" = "30 4 * * 1-5"
    "uat-ext02.night"   = "0 2 * * *"
  }
}

variable "autoscale_scheduling_extended_map" {
  description = "Cron map representation of autoscale scheduling for extended business hour scale-up/down on first/second of the month"
  type        = map(string)
  default = {
    "default.morning"   = ""
    "default.night"     = ""
    "uat-alpha.morning" = "58 11 1-2 * *"
    "uat-alpha.night"   = "2 6 2-3 * *"
    "uat-beta.morning"  = "58 3 1-2 * *"
    "uat-beta.night"    = "2 3 2-3 * *"
    "uat-ext01.morning" = "58 8 1-2 * *"
    "uat-ext01.night"   = "2 3 2-3 * *"
    "uat-ext02.morning" = "28 4 1-2 * *"
    "uat-ext02.night"   = "2 2 2-3 * *"
  }
}
