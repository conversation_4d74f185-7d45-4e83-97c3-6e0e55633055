#==============================================================
# ECS Cluster Module Variables
#==============================================================

###############################################################
# Required Variables from Outside
###############################################################

variable "environment" {
  description = "Name of the environment we are building"
}

variable "build_number" {
  description = "Build Number"
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
}

variable "region" {
  description = "AWS Region"
}

variable "region_abbreviated" {
  description = "Abbreviated AWS Region"
}

variable "component" {
  description = "Name of the component being deployed"
}

###############################################################
# Application Variables
###############################################################

variable "application" {
  description = "Name of the application to be used when tagging aws resources"
  default     = "ais10"
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  default     = "a10"
}

variable "service" {
  description = "The service that this configuration builds"
  default     = "web"
}

variable "slack_contact" {
  description = "Slack channel that should be notified by the various aws monkeys"
  default     = "+ais-operations"
}

###############################################################
# Network Variables
###############################################################

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network."
  default     = "************/32"
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network."
  default     = "***********/32"
}

variable "remote_cidr" {
  description = "The public CIDR block of the Remote networks."
  default     = "**************/32"
}

variable "vpc_id" {
  description = "VPC ID where the cluster will be deployed"
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs for the cluster"
  type        = list(string)
}

variable "availability_zones" {
  description = "List of availability zones"
  type        = list(string)
}

variable "security_group" {
  description = "Security group ID for EFS access"
}

variable "efs_id" {
  description = "EFS ID for shared storage"
}

variable "component_id" {
  description = "Component ID for tagging"
}

variable "nfs_cidr" {
  description = "CIDR block for NFS access"
}

###############################################################
# AutoScale Variables
###############################################################

variable "asg_min_size" {
  description = "Default min number of EC2s in autoscale group"
  default     = "3"
}

variable "asg_max_size" {
  description = "Default max number of EC2s in autoscale group"
  default     = "18"
}

variable "asg_desired_capacity" {
  description = "Default desired number of EC2s in autoscale group"
  default     = "3"
}

variable "asp_scale_out_adjustment" {
  description = "Instance side to use for 1.0 webstack"
  default     = "4"
}

variable "asp_scale_out_cooldown" {
  description = "Cooldown period for scale out"
  default     = "300"
}

variable "asp_scale_in_adjustment" {
  description = "Scale in adjustment"
  default     = "-2"
}

variable "asp_scale_in_cooldown" {
  description = "Cooldown period for scale in"
  default     = "300"
}

variable "asg_scheduling_enabled" {
  description = "Enable autoscaling scheduling"
  default     = "false"
}

variable "asg_extended_scheduling_enabled" {
  description = "Enable extended autoscaling scheduling"
  default     = "false"
}

variable "asg_scheduling_normal_map" {
  description = "Normal scheduling map for autoscaling"
  type        = map(string)
  default     = {
    "default.morning" = "0 12 * * MON-FRI"
    "default.night"   = "0 2 * * *"
  }
}

variable "asg_scheduling_extended_map" {
  description = "Extended scheduling map for autoscaling"
  type        = map(string)
  default     = {
    "default.morning" = "0 12 * * MON-FRI"
    "default.night"   = "0 2 * * *"
  }
}

variable "account_type" {
  description = "Account type (prod/nonprod)"
}

variable "asg_ami" {
  description = "AMI ID for autoscaling group instances"
}

variable "instance_type" {
  description = "EC2 instance type"
  default     = "m5.4xlarge"
}

variable "ec2_key_name" {
  description = "Name of the key pair to associate with EC2 instances"
  default     = "AIS10"
}

###############################################################
# ECS Cluster Variables
###############################################################

variable "ecs_logging" {
  default     = "[\"json-file\",\"awslogs\"]"
  description = "Adding logging option to ECS that the Docker containers can use. It is possible to add fluentd as well"
}

###############################################################
# CloudWatch Variables
###############################################################

variable "cw_alarm_low_cpu_threshold" {
  description = "The threshold value to use on the Low CPU CloudWatch alarm."
  default     = "20"
}

variable "cw_alarm_high_cpu_threshold" {
  description = "The threshold value to use on the High CPU CloudWatch alarm."
  default     = "80"
}

variable "cw_alarm_low_memory_threshold" {
  description = "The threshold value to use on the Low Memory CloudWatch alarm."
  default     = "20"
}

variable "cw_alarm_high_memory_threshold" {
  description = "The threshold value to use on the High Memory CloudWatch alarm."
  default     = "80"
}

###############################################################
# CloudWatch Alarm Period Variables
###############################################################

variable "cw_alarm_low_cpu_period" {
  description = "The period in seconds over which the specified statistic is applied for low CPU alarm."
  default     = "300"
}

variable "cw_alarm_low_cpu_evaluation_periods" {
  description = "The number of periods over which data is compared to the specified threshold for low CPU alarm."
  default     = "2"
}

variable "cw_alarm_high_cpu_period" {
  description = "The period in seconds over which the specified statistic is applied for high CPU alarm."
  default     = "300"
}

variable "cw_alarm_high_cpu_evaluation_periods" {
  description = "The number of periods over which data is compared to the specified threshold for high CPU alarm."
  default     = "2"
}

variable "cw_alarm_high_cpu_datapoint" {
  description = "The number of datapoints that must be breaching to trigger the high CPU alarm."
  default     = "2"
}

variable "cw_alarm_high_cpu_failsafe_threshold" {
  description = "The threshold value to use on the High CPU Failsafe CloudWatch alarm."
  default     = "90"
}

variable "cw_alarm_high_cpu_failsafe_evaluation_periods" {
  description = "The number of periods over which data is compared to the specified threshold for high CPU failsafe alarm."
  default     = "1"
}

variable "cw_alarm_high_cpu_failsafe_datapoint" {
  description = "The number of datapoints that must be breaching to trigger the high CPU failsafe alarm."
  default     = "1"
}

variable "cw_alarm_low_mem_threshold" {
  description = "The threshold value to use on the Low Memory CloudWatch alarm."
  default     = "20"
}

variable "cw_alarm_low_mem_period" {
  description = "The period in seconds over which the specified statistic is applied for low memory alarm."
  default     = "300"
}

variable "cw_alarm_low_mem_evaluation_periods" {
  description = "The number of periods over which data is compared to the specified threshold for low memory alarm."
  default     = "2"
}

variable "cw_alarm_high_mem_threshold" {
  description = "The threshold value to use on the High Memory CloudWatch alarm."
  default     = "80"
}

variable "cw_alarm_high_mem_period" {
  description = "The period in seconds over which the specified statistic is applied for high memory alarm."
  default     = "300"
}

variable "cw_alarm_high_mem_evaluation_periods" {
  description = "The number of periods over which data is compared to the specified threshold for high memory alarm."
  default     = "2"
}

variable "cw_alarm_high_mem_failsafe_threshold" {
  description = "The threshold value to use on the High Memory Failsafe CloudWatch alarm."
  default     = "90"
}

variable "cw_alarm_high_mem_failsafe_evaluation_periods" {
  description = "The number of periods over which data is compared to the specified threshold for high memory failsafe alarm."
  default     = "1"
}
