#==============================================================
# Module Outputs (accessible to consumers)
#==============================================================

output "cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.default.name
}

output "cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = aws_ecs_cluster.default.arn
}

output "cluster_id" {
  description = "ID of the ECS cluster"
  value       = aws_ecs_cluster.default.id
}

output "autoscaling_group_name" {
  description = "Name of the autoscaling group"
  value       = aws_autoscaling_group.default.name
}

output "autoscaling_group_arn" {
  description = "ARN of the autoscaling group"
  value       = aws_autoscaling_group.default.arn
}

output "security_group_id" {
  description = "ID of the security group created for the cluster"
  value       = aws_security_group.default.id
}

output "launch_configuration_name" {
  description = "Name of the launch configuration"
  value       = aws_launch_configuration.default.name
}

# CloudWatch Alarm outputs for reference
output "cpu_high_alarm_arn" {
  description = "ARN of the high CPU CloudWatch alarm"
  value       = aws_cloudwatch_metric_alarm.high_cpu.arn
}

output "cpu_low_alarm_arn" {
  description = "ARN of the low CPU CloudWatch alarm"
  value       = aws_cloudwatch_metric_alarm.low_cpu.arn
}

output "memory_high_alarm_arn" {
  description = "ARN of the high memory CloudWatch alarm"
  value       = aws_cloudwatch_metric_alarm.high_mem.arn
}

output "memory_low_alarm_arn" {
  description = "ARN of the low memory CloudWatch alarm"
  value       = aws_cloudwatch_metric_alarm.low_mem.arn
}

# Autoscaling policy outputs
output "scale_out_policy_arn" {
  description = "ARN of the scale out policy"
  value       = aws_autoscaling_policy.default-scale-out.arn
}

output "scale_in_policy_arn" {
  description = "ARN of the scale in policy"
  value       = aws_autoscaling_policy.default-scale-in.arn
}
