#==============================================================
# Component Outputs (accessible to other components)
#==============================================================

output "cluster_name" {
  description = "Name of the ECS cluster"
  value       = module.ecs-cluster.cluster_name
}

output "cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = module.ecs-cluster.cluster_arn
}

output "cluster_id" {
  description = "ID of the ECS cluster"
  value       = module.ecs-cluster.cluster_id
}

output "autoscaling_group_name" {
  description = "Name of the autoscaling group"
  value       = module.ecs-cluster.autoscaling_group_name
}

output "autoscaling_group_arn" {
  description = "ARN of the autoscaling group"
  value       = module.ecs-cluster.autoscaling_group_arn
}

output "security_group_id" {
  description = "ID of the security group created for the cluster"
  value       = module.ecs-cluster.security_group_id
}

output "launch_configuration_name" {
  description = "Name of the launch configuration"
  value       = module.ecs-cluster.launch_configuration_name
}

# CloudWatch Alarm outputs for reference
output "cpu_high_alarm_arn" {
  description = "ARN of the high CPU CloudWatch alarm"
  value       = module.ecs-cluster.cpu_high_alarm_arn
}

output "cpu_low_alarm_arn" {
  description = "ARN of the low CPU CloudWatch alarm"
  value       = module.ecs-cluster.cpu_low_alarm_arn
}

output "memory_high_alarm_arn" {
  description = "ARN of the high memory CloudWatch alarm"
  value       = module.ecs-cluster.memory_high_alarm_arn
}

output "memory_low_alarm_arn" {
  description = "ARN of the low memory CloudWatch alarm"
  value       = module.ecs-cluster.memory_low_alarm_arn
}

# Autoscaling policy outputs
output "scale_out_policy_arn" {
  description = "ARN of the scale out policy"
  value       = module.ecs-cluster.scale_out_policy_arn
}

output "scale_in_policy_arn" {
  description = "ARN of the scale in policy"
  value       = module.ecs-cluster.scale_in_policy_arn
}
