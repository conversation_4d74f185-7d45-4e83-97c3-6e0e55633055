# Separated AWS ECS Infrastructure

This directory contains the refactored AWS ECS infrastructure split into two independently deployable components:

## Components

### 1. Cluster Module (`cluster-module/`)
Handles all cluster-level infrastructure:
- ECS cluster configuration
- VPC and subnet configuration  
- Security groups
- Auto Scaling Groups
- Load balancers
- CloudWatch alarms
- Any other cluster-level resources

### 2. Task Module (`task-module/`)
Handles application-specific resources:
- ECS task definitions
- ECS services
- Application-specific configurations
- Service discovery resources
- Scheduled events
- Log groups
- Application alarms

## Usage

### Deploy Cluster Module First
```bash
cd cluster-module/environments/{environment}/processing-apps-cluster
terraform init
terraform plan
terraform apply
```

### Deploy Task Module Second
```bash
cd task-module/environments/{environment}/task-definitions  
terraform init
terraform plan
terraform apply
```

## Dependencies

The Task Module depends on outputs from the Cluster Module via Terraform remote state. The cluster must be deployed before the task module.

## Migration from Original Infrastructure

See [MIGRATION.md](MIGRATION.md) for detailed instructions on migrating from the original `infrastructure/` directory.
