resource "aws_cloudwatch_event_rule" "default" {
  name                = "${var.task_friendly_name}_${var.environment}_schedule"
  description         = "Scheduled event for the ${var.task_friendly_name}_${var.environment} processing app"
  schedule_expression = "cron(${var.schedule_expression})"
  role_arn            = var.role_arn
  is_enabled          = var.enabled

  lifecycle {
    ignore_changes = [is_enabled]
  }
}

resource "aws_cloudwatch_event_target" "default" {
  rule     = aws_cloudwatch_event_rule.default.name
  arn      = var.cluster_arn
  role_arn = var.role_arn

  ecs_target {
    task_count          = var.task_count
    task_definition_arn = var.task_arn
  }
}

