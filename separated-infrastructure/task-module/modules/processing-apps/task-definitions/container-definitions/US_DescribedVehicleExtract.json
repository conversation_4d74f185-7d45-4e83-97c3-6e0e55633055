[{"name": "${task_friendly_name}", "image": "${image_url_name_tag}", "cpu": 2048, "memory": 4096, "environment": [{"name": "ENVIRONMENT", "value": "${environment}"}, {"name": "COUNTRY", "value": "${country_iso_code}"}, {"name": "AWS_REGION", "value": "${region}"}, {"name": "TASKNAME", "value": "${task_friendly_name}"}, {"name": "DOTNET_ENVIRONMENT", "value": "${environment}"}], "secrets": [{"name": "DbConnectionInfo__Password", "valueFrom": "${db_password_arn}"}, {"name": "EmailSettings__SmtpPassword", "valueFrom": "${email_smtp_password_arn}"}], "mountPoints": [{"sourceVolume": "aisdata", "containerPath": "/app/aisdata", "readonly": false}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "${task_friendly_name}_${environment}", "awslogs-region": "${region}", "awslogs-stream-prefix": "ecstasks"}}, "healthCheck": {"command": ["CMD-SHELL", "dotnet --info || exit 1"], "interval": 30, "timeout": 10, "retries": 3, "startPeriod": 60}, "essential": true}]