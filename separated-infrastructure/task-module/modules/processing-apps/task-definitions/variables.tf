#==============================================================
# Task Definitions Module Variables
#==============================================================

###############################################################
# Required Variables from Outside
###############################################################

variable "environment" {
  description = "Name of the environment we are building"
}

variable "build_number" {
  description = "Build Number"
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
}

variable "region" {
  description = "AWS Region"
}

variable "component" {
  description = "Name of the component being deployed"
}

###############################################################
# Application Variables
###############################################################

variable "application" {
  description = "Name of the application to be used when tagging aws resources"
  default     = "ais10"
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  default     = "a10"
}

variable "service" {
  description = "The service that this configuration builds"
  default     = "web"
}

variable "slack_contact" {
  description = "Slack channel that should be notified by the various aws monkeys"
  default     = "+ais-operations"
}

###############################################################
# Task Definition Variables
###############################################################

variable "country_iso_code" {
  description = "Country ISO code for the task"
}

variable "task_friendly_name" {
  description = "Friendly name for the task"
}

variable "container_definition_path" {
  description = "Path to the container definition json file"
}

variable "task_role_arn" {
  description = "The ARN of IAM role that allows your Amazon ECS container task to make calls to other AWS services."
}

variable "execution_role_arn" {
  description = "The Amazon Resource Name (ARN) of the task execution role that the Amazon ECS container agent and the Docker daemon can assume."
}

variable "requires_compatibilites" {
  description = "A set of launch types required by the task. The valid values are EC2 and FARGATE."
  default     = "EC2"
}

variable "image_url_name_tag" {
  description = "The ECR URL and image name:tag that should be used for the container."
}

variable "rrri_topic_arn" {
  description = "ARN of the RRRI SNS topic"
}

###############################################################
# Cluster Reference Variables (from remote state)
###############################################################

variable "cluster_arn" {
  description = "ARN of the ECS cluster (from remote state)"
}

###############################################################
# Log Group Variables
###############################################################

variable "retention_in_days" {
  description = "Log retention period in days"
  default     = 7
}

###############################################################
# Scheduled Event Variables
###############################################################

variable "schedule_expression" {
  description = "Schedule expression for the task"
}

variable "event_rule_arn" {
  description = "ARN of the event rule role"
}

variable "task_count" {
  description = "Number of tasks to run"
  default     = 1
}

variable "enabled" {
  description = "Whether the scheduled event is enabled"
  default     = true
}

###############################################################
# Alarm Variables
###############################################################

variable "alarm_action_arn" {
  description = "ARN for alarm actions"
}

variable "alarm_description" {
  description = "Description for the alarm"
}

variable "alarm_metric_filter_pattern" {
  description = "Metric filter pattern for the alarm"
}
