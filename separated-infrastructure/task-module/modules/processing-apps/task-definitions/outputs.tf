#==============================================================
# Module Outputs (accessible to consumers)
#==============================================================

output "task_friendly_name" {
  description = "Friendly name of the task"
  value       = var.task_friendly_name
}

output "task_arn" {
  description = "ARN of the task definition"
  value       = module.task.task_arn
}

output "task_family" {
  description = "Family of the task definition"
  value       = module.task.task_family
}

output "task_revision" {
  description = "Revision of the task definition"
  value       = module.task.task_revision
}

output "log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = module.log-group.log_group_name
}

output "log_group_arn" {
  description = "ARN of the CloudWatch log group"
  value       = module.log-group.log_group_arn
}

output "scheduled_event_rule_name" {
  description = "Name of the scheduled event rule"
  value       = module.scheduled-event.event_rule_name
}

output "scheduled_event_rule_arn" {
  description = "ARN of the scheduled event rule"
  value       = module.scheduled-event.event_rule_arn
}

output "alarm_name" {
  description = "Name of the CloudWatch alarm"
  value       = module.alarm.alarm_name
}

output "alarm_arn" {
  description = "ARN of the CloudWatch alarm"
  value       = module.alarm.alarm_arn
}
